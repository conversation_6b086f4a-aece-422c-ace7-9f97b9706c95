import type { ApiResponse } from './auth'
import { cachedApi, CachePresets, generateCacheKey } from './cachedApi'
import api from './index'

// 成品相关类型定义
export interface Product {
  id: number
  code: string
  name: string
  specification?: string
  unit: string
  cost_price: number
  sale_price: number
  stock_min: number
  stock_max: number
  current_stock: number
  status: 'active' | 'inactive'
  created_at: string
  updated_at: string
}

export interface ProductForm {
  code: string
  name: string
  specification?: string
  unit: string
  cost_price: number
  sale_price: number
  stock_min: number
  stock_max: number
  current_stock?: number
  status?: 'active' | 'inactive'
}

export interface ProductQuery {
  page?: number
  pageSize?: number
  search?: string
  status?: 'active' | 'inactive'
}

export interface ProductListResponse {
  products: Product[]
  total: number
  page: number
  pageSize: number
}

// 成品管理API
export const productApi = {
  // 获取成品列表（带缓存）
  async getProducts(params: ProductQuery = {}): Promise<ProductListResponse> {
    const cacheKey = generateCacheKey('products', params)
    const cachedResponse = await cachedApi.get(
      () => api.get<any>('/products', { params }),
      cacheKey,
      CachePresets.MEDIUM
    )
    return cachedResponse.data
  },

  // 获取单个成品详情（带缓存）
  async getProductById(id: number): Promise<Product> {
    const cacheKey = `product_${id}`
    const cachedResponse = await cachedApi.get(
      () => api.get<ApiResponse<Product>>(`/products/${id}`),
      cacheKey,
      CachePresets.LONG
    )
    return cachedResponse.data!
  },

  // 创建成品（清除相关缓存）
  async createProduct(data: ProductForm): Promise<{ id: number }> {
    const response = await api.post<ApiResponse<{ id: number }>>('/products', data)
    // 清除列表缓存
    cachedApi.invalidatePattern('products')
    return response.data.data!
  },

  // 更新成品（清除相关缓存）
  async updateProduct(id: number, data: ProductForm): Promise<void> {
    await api.put(`/products/${id}`, data)
    // 清除相关缓存
    cachedApi.invalidate(`product_${id}`)
    cachedApi.invalidatePattern('products')
  },

  // 删除成品（清除相关缓存）
  async deleteProduct(id: number): Promise<void> {
    await api.delete(`/products/${id}`)
    // 清除相关缓存
    cachedApi.invalidate(`product_${id}`)
    cachedApi.invalidatePattern('products')
  }
}
