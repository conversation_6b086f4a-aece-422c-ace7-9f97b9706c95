import './assets/main.css'

import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import { createPinia } from 'pinia'
import { createApp } from 'vue'
import App from './App.vue'
import { setupPermissionDirectives } from './directives/permission'
import { registerIcons } from './plugins/icons'
import router from './router'
import { createPersistencePlugin, persistenceConfigs } from './utils/persistence'
import { createStateTrackerPlugin } from './utils/stateTracker'

const app = createApp(App)

// 注册按需引入的图标
registerIcons(app)

// 注册权限指令
setupPermissionDirectives(app)

// 创建Pinia实例并配置插件
const pinia = createPinia()
pinia.use(createPersistencePlugin(persistenceConfigs))
pinia.use(createStateTrackerPlugin())
app.use(pinia)
app.use(router)
app.use(ElementPlus)

// 初始化错误报告服务
console.log('错误报告服务已启动')

app.mount('#app')
