/**
 * 带缓存的API包装器
 * 为30人工厂提供简单实用的前端缓存功能
 */

import { globalCacheManager } from '@/utils/cacheManager'
import type { ApiResponse } from './auth'

export interface CacheConfig {
  // 缓存时间（毫秒）
  ttl?: number
  // 缓存键前缀
  keyPrefix?: string
  // 是否启用缓存
  enabled?: boolean
  // 是否在后台刷新
  backgroundRefresh?: boolean
}

export interface CachedApiOptions extends CacheConfig {
  // 缓存键生成函数
  keyGenerator?: (url: string, params?: any) => string
  // 数据验证函数
  validator?: (data: any) => boolean
}

class CachedApiWrapper {
  private defaultConfig: Required<CacheConfig> = {
    ttl: 5 * 60 * 1000, // 5分钟
    keyPrefix: 'api_',
    enabled: true,
    backgroundRefresh: false
  }

  /**
   * 带缓存的GET请求
   */
  async get<T>(
    apiCall: () => Promise<ApiResponse<T>>,
    cacheKey: string,
    options: CachedApiOptions = {}
  ): Promise<ApiResponse<T>> {
    const config = { ...this.defaultConfig, ...options }
    
    if (!config.enabled) {
      return apiCall()
    }

    const fullKey = config.keyPrefix + cacheKey

    // 尝试从缓存获取
    const cached = globalCacheManager.get<ApiResponse<T>>(fullKey)
    if (cached && this.isValidCachedData(cached, config.validator)) {
      // 如果启用后台刷新，异步更新缓存
      if (config.backgroundRefresh) {
        this.refreshInBackground(apiCall, fullKey, config)
      }
      return cached
    }

    // 缓存未命中，调用API
    try {
      const response = await apiCall()
      
      // 只缓存成功的响应
      if (response.success) {
        globalCacheManager.set(fullKey, response, config.ttl)
      }
      
      return response
    } catch (error) {
      // 如果有过期的缓存数据，在网络错误时返回
      if (cached) {
        console.warn('API调用失败，返回过期缓存数据:', error)
        return cached
      }
      throw error
    }
  }

  /**
   * 使缓存失效
   */
  invalidate(cacheKey: string, keyPrefix?: string): void {
    const prefix = keyPrefix || this.defaultConfig.keyPrefix
    const fullKey = prefix + cacheKey
    globalCacheManager.delete(fullKey)
  }

  /**
   * 批量使缓存失效
   */
  invalidatePattern(pattern: string, keyPrefix?: string): void {
    const prefix = keyPrefix || this.defaultConfig.keyPrefix
    const fullPattern = prefix + pattern
    globalCacheManager.clear() // 简化实现，清空所有缓存
  }

  /**
   * 预加载数据到缓存
   */
  async preload<T>(
    apiCall: () => Promise<ApiResponse<T>>,
    cacheKey: string,
    options: CachedApiOptions = {}
  ): Promise<void> {
    try {
      await this.get(apiCall, cacheKey, options)
    } catch (error) {
      console.warn('预加载缓存失败:', error)
    }
  }

  /**
   * 后台刷新缓存
   */
  private async refreshInBackground<T>(
    apiCall: () => Promise<ApiResponse<T>>,
    cacheKey: string,
    config: Required<CacheConfig>
  ): Promise<void> {
    try {
      const response = await apiCall()
      if (response.success) {
        globalCacheManager.set(cacheKey, response, config.ttl)
      }
    } catch (error) {
      console.warn('后台刷新缓存失败:', error)
    }
  }

  /**
   * 验证缓存数据是否有效
   */
  private isValidCachedData(data: any, validator?: (data: any) => boolean): boolean {
    if (!data) return false
    
    if (validator) {
      return validator(data)
    }
    
    // 基本验证：检查是否有success字段
    return typeof data === 'object' && 'success' in data
  }

  /**
   * 获取缓存统计信息
   */
  getStats() {
    return globalCacheManager.getStats()
  }

  /**
   * 清空所有API缓存
   */
  clearAll(): void {
    globalCacheManager.clear()
  }
}

// 创建全局实例
export const cachedApi = new CachedApiWrapper()

// 预定义的缓存配置
export const CachePresets = {
  // 短期缓存（1分钟）- 用于频繁变化的数据
  SHORT: {
    ttl: 1 * 60 * 1000,
    backgroundRefresh: true
  },
  
  // 中期缓存（5分钟）- 用于一般业务数据
  MEDIUM: {
    ttl: 5 * 60 * 1000,
    backgroundRefresh: true
  },
  
  // 长期缓存（30分钟）- 用于基础数据
  LONG: {
    ttl: 30 * 60 * 1000,
    backgroundRefresh: false
  },
  
  // 静态缓存（2小时）- 用于很少变化的数据
  STATIC: {
    ttl: 2 * 60 * 60 * 1000,
    backgroundRefresh: false
  }
}

// 便捷的缓存API函数
export const createCachedApi = <T>(
  apiCall: () => Promise<ApiResponse<T>>,
  cacheKey: string,
  preset: keyof typeof CachePresets = 'MEDIUM'
) => {
  return () => cachedApi.get(apiCall, cacheKey, CachePresets[preset])
}

// 生成缓存键的工具函数
export const generateCacheKey = (endpoint: string, params?: any): string => {
  if (!params) return endpoint
  
  // 将参数转换为稳定的字符串
  const sortedParams = Object.keys(params)
    .sort()
    .reduce((result, key) => {
      result[key] = params[key]
      return result
    }, {} as any)
  
  return `${endpoint}_${JSON.stringify(sortedParams)}`
}

// 缓存管理工具
export const cacheUtils = {
  /**
   * 清除特定模块的缓存
   */
  clearModule(module: string) {
    cachedApi.invalidatePattern(module)
  },

  /**
   * 预加载常用数据
   */
  async preloadCommonData() {
    // 这里可以预加载一些常用的数据
    console.log('预加载常用数据...')
  },

  /**
   * 获取缓存使用情况
   */
  getCacheUsage() {
    return cachedApi.getStats()
  }
}
