import cors from 'cors';
import dotenv from 'dotenv';
import express from 'express';
import { createCORSOptions, enhanceCORSMiddleware } from './config/corsConfig';
import { createRateLimiter, getRateLimitStatus, RateLimitStrategy } from './config/rateLimitConfig';
import {
    errorHandler,
    notFoundHandler,
    requestLogger,
    setupProcessErrorHandlers
} from './middleware/errorHandler';
import authRoutes from './routes/auth';
import customerRoutes from './routes/customers';
import inventoryRoutes from './routes/inventory';
import materialRoutes from './routes/materials';
import poolRoutes from './routes/pool';
import productionCompletionRoutes from './routes/productionCompletions';
import productionPlanRoutes from './routes/productionPlans';
import productRoutes from './routes/products';
import purchaseOrderRoutes from './routes/purchaseOrders';
import purchaseReceiptRoutes from './routes/purchaseReceipts';
import reportsRoutes from './routes/reports';
import salesDeliveryRoutes from './routes/salesDeliveries';
import salesOrderRoutes from './routes/salesOrders';
import stocktakingRoutes from './routes/stocktaking';
import supplierRoutes from './routes/suppliers';

// 加载环境变量
dotenv.config();

// 设置进程级错误处理
setupProcessErrorHandlers();

const app = express();
const PORT = process.env.PORT || 3000;

// H008 - 安全的CORS配置
const corsOptions = createCORSOptions();

// 基础中间件
app.use(cors(corsOptions));

// CORS增强中间件
app.use((req, res, next) => {
  enhanceCORSMiddleware(req);
  next();
});

// H010 - API请求频率限制
// 注意：特定端点的限制在路由中单独配置，这里不设置全局限制
// 全局限制会在所有路由之后作为兜底策略

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 请求日志中间件（如果启用）
if (process.env.ENABLE_REQUEST_LOGGING !== 'false') {
  app.use(requestLogger);
}

// 性能监控中间件
if (process.env.ENABLE_PERFORMANCE_MONITORING !== 'false') {
  app.use(performanceMonitorMiddleware);
}

// 基础路由
app.get('/', (req, res) => {
  res.json({ message: 'ERP进销存管理系统 API 服务器运行中' });
});

// 健康检查
import { getHealthStatus, getSimpleHealth } from './controllers/healthController';
app.get('/health', getHealthStatus);
app.get('/health/simple', getSimpleHealth);

// Rate Limiting状态查询（开发和测试环境）
if (process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'test') {
  app.get('/rate-limit-status', (req, res) => {
    res.json(getRateLimitStatus());
  });
}

// API路由 - 应用特定的rate limiting策略
// 认证相关API - 严格限制
app.use('/api/auth',
  process.env.RATE_LIMIT_ENABLED !== 'false' ? createRateLimiter(RateLimitStrategy.STRICT) : (req, res, next) => next(),
  authRoutes
);
// 基础数据管理API - 中等限制
app.use('/api/materials',
  process.env.RATE_LIMIT_ENABLED !== 'false' ? createRateLimiter(RateLimitStrategy.MODERATE) : (req, res, next) => next(),
  materialRoutes
);
app.use('/api/products',
  process.env.RATE_LIMIT_ENABLED !== 'false' ? createRateLimiter(RateLimitStrategy.MODERATE) : (req, res, next) => next(),
  productRoutes
);
app.use('/api/suppliers',
  process.env.RATE_LIMIT_ENABLED !== 'false' ? createRateLimiter(RateLimitStrategy.MODERATE) : (req, res, next) => next(),
  supplierRoutes
);
app.use('/api/customers',
  process.env.RATE_LIMIT_ENABLED !== 'false' ? createRateLimiter(RateLimitStrategy.MODERATE) : (req, res, next) => next(),
  customerRoutes
);

// 业务流程API - 中等限制
app.use('/api/purchase-orders',
  process.env.RATE_LIMIT_ENABLED !== 'false' ? createRateLimiter(RateLimitStrategy.MODERATE) : (req, res, next) => next(),
  purchaseOrderRoutes
);
app.use('/api/purchase-receipts',
  process.env.RATE_LIMIT_ENABLED !== 'false' ? createRateLimiter(RateLimitStrategy.MODERATE) : (req, res, next) => next(),
  purchaseReceiptRoutes
);
app.use('/api/sales-orders',
  process.env.RATE_LIMIT_ENABLED !== 'false' ? createRateLimiter(RateLimitStrategy.MODERATE) : (req, res, next) => next(),
  salesOrderRoutes
);
app.use('/api/sales-deliveries',
  process.env.RATE_LIMIT_ENABLED !== 'false' ? createRateLimiter(RateLimitStrategy.MODERATE) : (req, res, next) => next(),
  salesDeliveryRoutes
);
app.use('/api/production-plans',
  process.env.RATE_LIMIT_ENABLED !== 'false' ? createRateLimiter(RateLimitStrategy.MODERATE) : (req, res, next) => next(),
  productionPlanRoutes
);
app.use('/api/production-completions',
  process.env.RATE_LIMIT_ENABLED !== 'false' ? createRateLimiter(RateLimitStrategy.MODERATE) : (req, res, next) => next(),
  productionCompletionRoutes
);

// 查询类API - 标准限制
app.use('/api/inventory',
  process.env.RATE_LIMIT_ENABLED !== 'false' ? createRateLimiter(RateLimitStrategy.STANDARD) : (req, res, next) => next(),
  inventoryRoutes
);
app.use('/api/stocktaking',
  process.env.RATE_LIMIT_ENABLED !== 'false' ? createRateLimiter(RateLimitStrategy.MODERATE) : (req, res, next) => next(),
  stocktakingRoutes
);

// 报表API - 资源密集型限制
app.use('/api/reports',
  process.env.RATE_LIMIT_ENABLED !== 'false' ? createRateLimiter(RateLimitStrategy.RESOURCE_INTENSIVE) : (req, res, next) => next(),
  reportsRoutes
);

// 性能监控API - 管理员专用
app.use('/api/performance',
  process.env.RATE_LIMIT_ENABLED !== 'false' ? createRateLimiter(RateLimitStrategy.STRICT) : (req, res, next) => next(),
  performanceRoutes
);

// 连接池管理API - 管理员专用
app.use('/api/pool',
  process.env.RATE_LIMIT_ENABLED !== 'false' ? createRateLimiter(RateLimitStrategy.STRICT) : (req, res, next) => next(),
  poolRoutes
);

// 权限管理API - 管理员专用
app.use('/api/permissions',
  process.env.RATE_LIMIT_ENABLED !== 'false' ? createRateLimiter(RateLimitStrategy.STRICT) : (req, res, next) => next(),
  permissionRoutes
);

// 全局兜底Rate Limiting（对未匹配特定限制的路由）
if (process.env.RATE_LIMIT_ENABLED !== 'false') {
  app.use(createRateLimiter(RateLimitStrategy.STANDARD));
}

// 404错误处理中间件（必须在所有路由之后）
app.use(notFoundHandler);

// 全局错误处理中间件（必须在最后）
app.use(errorHandler);

// 启动服务器
async function startServer() {
  try {
    // 初始化数据库连接池
    await initializeDatabase();
    console.log('数据库连接池初始化成功');

    app.listen(PORT, () => {
      console.log(`服务器运行在端口 ${PORT}`);
      console.log(`API地址: http://localhost:${PORT}`);
    });
  } catch (error) {
    console.error('服务器启动失败:', error);
    process.exit(1);
  }
}

startServer();
