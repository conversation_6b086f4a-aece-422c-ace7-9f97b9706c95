import type { ApiResponse } from './auth'
import { cachedApi, CachePresets, generateCacheKey } from './cachedApi'
import api from './index'

// 供应商相关类型定义
export interface Supplier {
  id: number
  code: string
  name: string
  contact_person?: string
  phone?: string
  address?: string
  settlement_method?: string
  status: 'active' | 'inactive'
  created_at: string
  updated_at: string
}

export interface SupplierForm {
  code: string
  name: string
  contact_person?: string
  phone?: string
  address?: string
  settlement_method?: string
}

export interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  limit: number
  totalPages: number
}

export interface SupplierQuery {
  page?: number
  limit?: number
  search?: string
}

// 供应商API
export const supplierApi = {
  // 获取供应商列表（带缓存）
  getSuppliers(params?: SupplierQuery): Promise<ApiResponse<PaginatedResponse<Supplier>>> {
    const cacheKey = generateCacheKey('suppliers', params)
    return cachedApi.get(
      () => api.get('/suppliers', { params }),
      cacheKey,
      CachePresets.MEDIUM
    )
  },

  // 获取单个供应商（带缓存）
  getSupplier(id: number): Promise<ApiResponse<Supplier>> {
    const cacheKey = `supplier_${id}`
    return cachedApi.get(
      () => api.get(`/suppliers/${id}`),
      cacheKey,
      CachePresets.LONG
    )
  },

  // 创建供应商（清除相关缓存）
  async createSupplier(data: SupplierForm): Promise<ApiResponse> {
    const response = await api.post('/suppliers', data)
    if (response.success) {
      // 清除列表缓存
      cachedApi.invalidatePattern('suppliers')
    }
    return response
  },

  // 更新供应商（清除相关缓存）
  async updateSupplier(id: number, data: Partial<SupplierForm>): Promise<ApiResponse> {
    const response = await api.put(`/suppliers/${id}`, data)
    if (response.success) {
      // 清除相关缓存
      cachedApi.invalidate(`supplier_${id}`)
      cachedApi.invalidatePattern('suppliers')
    }
    return response
  },

  // 删除供应商（清除相关缓存）
  async deleteSupplier(id: number): Promise<ApiResponse> {
    const response = await api.delete(`/suppliers/${id}`)
    if (response.success) {
      // 清除相关缓存
      cachedApi.invalidate(`supplier_${id}`)
      cachedApi.invalidatePattern('suppliers')
    }
    return response
  }
}
