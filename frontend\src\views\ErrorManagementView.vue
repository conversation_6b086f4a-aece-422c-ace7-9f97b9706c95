<template>
  <div class="error-management-container">
    <div class="page-title">
      <h1>错误管理</h1>
      <p class="subtitle">查看和管理系统错误信息</p>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-number">{{ errorStats.total }}</div>
          <div class="stat-label">总错误数</div>
        </div>
      </el-card>
      
      <el-card class="stat-card error">
        <div class="stat-content">
          <div class="stat-number">{{ errorStats.byLevel.error }}</div>
          <div class="stat-label">严重错误</div>
        </div>
      </el-card>
      
      <el-card class="stat-card warning">
        <div class="stat-content">
          <div class="stat-number">{{ errorStats.byLevel.warning }}</div>
          <div class="stat-label">警告</div>
        </div>
      </el-card>
      
      <el-card class="stat-card info">
        <div class="stat-content">
          <div class="stat-number">{{ errorStats.byLevel.info }}</div>
          <div class="stat-label">信息</div>
        </div>
      </el-card>
    </div>

    <!-- 操作栏 -->
    <div class="operations-bar">
      <div class="operations-left">
        <el-select v-model="filterLevel" placeholder="筛选错误级别" clearable>
          <el-option label="全部" value="" />
          <el-option label="错误" value="error" />
          <el-option label="警告" value="warning" />
          <el-option label="信息" value="info" />
        </el-select>
        
        <el-input
          v-model="searchQuery"
          placeholder="搜索错误信息"
          style="width: 300px"
          clearable
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
      
      <div class="operations-right">
        <el-button @click="refreshErrors">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        
        <el-button type="primary" @click="exportErrors">
          <el-icon><Download /></el-icon>
          导出
        </el-button>
        
        <el-button type="danger" @click="clearErrors">
          <el-icon><Delete /></el-icon>
          清空
        </el-button>
      </div>
    </div>

    <!-- 错误列表 -->
    <el-card class="error-list-card">
      <el-table
        :data="filteredErrors"
        style="width: 100%"
        :default-sort="{ prop: 'timestamp', order: 'descending' }"
      >
        <el-table-column prop="timestamp" label="时间" width="180" sortable>
          <template #default="{ row }">
            {{ formatTime(row.timestamp) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="level" label="级别" width="80">
          <template #default="{ row }">
            <el-tag :type="getLevelType(row.level)">
              {{ getLevelText(row.level) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="component" label="组件" width="120" />
        
        <el-table-column prop="message" label="错误信息" min-width="200" show-overflow-tooltip />
        
        <el-table-column prop="action" label="操作" width="120" />
        
        <el-table-column label="操作" width="100">
          <template #default="{ row }">
            <el-button type="primary" text @click="showErrorDetail(row)">
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 错误详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      title="错误详情"
      width="80%"
      :before-close="handleDetailClose"
    >
      <div v-if="selectedError" class="error-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="时间">
            {{ formatTime(selectedError.timestamp) }}
          </el-descriptions-item>
          <el-descriptions-item label="级别">
            <el-tag :type="getLevelType(selectedError.level)">
              {{ getLevelText(selectedError.level) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="组件">
            {{ selectedError.component || '未知' }}
          </el-descriptions-item>
          <el-descriptions-item label="操作">
            {{ selectedError.action || '未知' }}
          </el-descriptions-item>
          <el-descriptions-item label="用户ID">
            {{ selectedError.userId || '匿名' }}
          </el-descriptions-item>
          <el-descriptions-item label="页面URL">
            {{ selectedError.url }}
          </el-descriptions-item>
        </el-descriptions>
        
        <div class="error-message">
          <h4>错误信息</h4>
          <pre>{{ selectedError.message }}</pre>
        </div>
        
        <div v-if="selectedError.stack" class="error-stack">
          <h4>堆栈信息</h4>
          <pre>{{ selectedError.stack }}</pre>
        </div>
        
        <div v-if="selectedError.extra" class="error-extra">
          <h4>额外信息</h4>
          <pre>{{ JSON.stringify(selectedError.extra, null, 2) }}</pre>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh, Download, Delete } from '@element-plus/icons-vue'
import { errorReporting, type ErrorReport } from '@/utils/errorReporting'

// 响应式数据
const errors = ref<ErrorReport[]>([])
const filterLevel = ref('')
const searchQuery = ref('')
const showDetailDialog = ref(false)
const selectedError = ref<ErrorReport | null>(null)

// 计算属性
const errorStats = computed(() => errorReporting.getErrorStats())

const filteredErrors = computed(() => {
  let filtered = errors.value

  // 按级别筛选
  if (filterLevel.value) {
    filtered = filtered.filter(error => error.level === filterLevel.value)
  }

  // 按搜索关键词筛选
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(error => 
      error.message.toLowerCase().includes(query) ||
      error.component?.toLowerCase().includes(query) ||
      error.action?.toLowerCase().includes(query)
    )
  }

  return filtered
})

// 方法
function refreshErrors() {
  errors.value = errorReporting.getLocalErrors()
  ElMessage.success('错误列表已刷新')
}

function clearErrors() {
  ElMessageBox.confirm(
    '确定要清空所有错误记录吗？此操作不可恢复。',
    '确认清空',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    errorReporting.clearLocalErrors()
    errors.value = []
    ElMessage.success('错误记录已清空')
  }).catch(() => {
    // 用户取消
  })
}

function exportErrors() {
  const data = JSON.stringify(errors.value, null, 2)
  const blob = new Blob([data], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  
  const a = document.createElement('a')
  a.href = url
  a.download = `errors_${new Date().toISOString().split('T')[0]}.json`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
  
  ElMessage.success('错误记录已导出')
}

function showErrorDetail(error: ErrorReport) {
  selectedError.value = error
  showDetailDialog.value = true
}

function handleDetailClose() {
  showDetailDialog.value = false
  selectedError.value = null
}

function formatTime(timestamp: string) {
  return new Date(timestamp).toLocaleString('zh-CN')
}

function getLevelType(level: string) {
  switch (level) {
    case 'error': return 'danger'
    case 'warning': return 'warning'
    case 'info': return 'info'
    default: return ''
  }
}

function getLevelText(level: string) {
  switch (level) {
    case 'error': return '错误'
    case 'warning': return '警告'
    case 'info': return '信息'
    default: return level
  }
}

// 生命周期
onMounted(() => {
  refreshErrors()
})
</script>

<style scoped>
.error-management-container {
  padding: 20px;
}

.page-title {
  margin-bottom: 24px;
}

.page-title h1 {
  margin: 0 0 8px 0;
  color: #303133;
}

.subtitle {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stat-card {
  cursor: pointer;
  transition: transform 0.2s;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-card.error {
  border-left: 4px solid #f56c6c;
}

.stat-card.warning {
  border-left: 4px solid #e6a23c;
}

.stat-card.info {
  border-left: 4px solid #409eff;
}

.stat-content {
  text-align: center;
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 8px;
}

.stat-label {
  color: #606266;
  font-size: 14px;
}

.operations-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  flex-wrap: wrap;
  gap: 16px;
}

.operations-left {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
}

.operations-right {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.error-list-card {
  margin-bottom: 20px;
}

.error-detail {
  max-height: 60vh;
  overflow-y: auto;
}

.error-message,
.error-stack,
.error-extra {
  margin-top: 16px;
}

.error-message h4,
.error-stack h4,
.error-extra h4 {
  margin: 0 0 8px 0;
  color: #303133;
}

.error-message pre,
.error-stack pre,
.error-extra pre {
  background-color: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  font-size: 12px;
  line-height: 1.4;
  white-space: pre-wrap;
  word-break: break-all;
  max-height: 300px;
  overflow-y: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .operations-bar {
    flex-direction: column;
    align-items: stretch;
  }
  
  .operations-left,
  .operations-right {
    width: 100%;
    justify-content: center;
  }
  
  .stats-cards {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>
