# JJS生产管理软件 API文档

## 概述

本文档描述了JJS生产管理软件的后端API接口，适用于30人左右的工厂生产管理需求。

**基础信息：**
- 基础URL: `http://localhost:3000/api`
- 认证方式: JWT Token
- 数据格式: JSON
- 字符编码: UTF-8

## 认证

### 登录
```http
POST /auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "password"
}
```

**响应：**
```json
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": 1,
      "username": "admin",
      "role": "admin"
    }
  }
}
```

### 请求头
所有需要认证的接口都需要在请求头中包含：
```
Authorization: Bearer <token>
```

## 系统监控

### 健康检查
```http
GET /health
```

**响应：**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "uptime": 3600,
  "version": "1.0.0",
  "environment": "production",
  "database": {
    "status": "connected",
    "responseTime": 5
  },
  "system": {
    "memory": {
      "used": "256 MB",
      "total": "1 GB",
      "percentage": 25
    },
    "cpu": {
      "loadAverage": [0.5, 0.3, 0.2],
      "usage": 50
    },
    "disk": {
      "used": "2 GB",
      "total": "10 GB",
      "percentage": 20
    }
  }
}
```

### 简单健康检查
```http
GET /health/simple
```

**响应：**
```json
{
  "status": "OK",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "uptime": 3600
}
```

## 原材料管理

### 获取原材料列表
```http
GET /materials?page=1&limit=10&search=钢材
```

**查询参数：**
- `page`: 页码（默认1）
- `limit`: 每页数量（默认10）
- `search`: 搜索关键词
- `category`: 分类筛选

**响应：**
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": 1,
        "name": "钢材A",
        "category": "金属",
        "unit": "吨",
        "price": 5000.00,
        "stock": 100,
        "minStock": 10,
        "supplier": "供应商A",
        "createdAt": "2024-01-01T00:00:00.000Z",
        "updatedAt": "2024-01-01T00:00:00.000Z"
      }
    ],
    "total": 50,
    "page": 1,
    "limit": 10,
    "totalPages": 5
  }
}
```

### 获取单个原材料
```http
GET /materials/{id}
```

### 创建原材料
```http
POST /materials
Content-Type: application/json

{
  "name": "钢材B",
  "category": "金属",
  "unit": "吨",
  "price": 5200.00,
  "stock": 50,
  "minStock": 5,
  "supplier": "供应商B",
  "description": "高强度钢材"
}
```

### 更新原材料
```http
PUT /materials/{id}
Content-Type: application/json

{
  "price": 5300.00,
  "stock": 80
}
```

### 删除原材料
```http
DELETE /materials/{id}
```

## 成品管理

### 获取成品列表
```http
GET /products?page=1&limit=10&search=产品A
```

**响应：**
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": 1,
        "name": "产品A",
        "model": "PA-001",
        "category": "机械",
        "price": 15000.00,
        "stock": 20,
        "minStock": 2,
        "status": "active",
        "materials": [
          {
            "materialId": 1,
            "materialName": "钢材A",
            "quantity": 2,
            "unit": "吨"
          }
        ],
        "createdAt": "2024-01-01T00:00:00.000Z"
      }
    ],
    "total": 30,
    "page": 1,
    "limit": 10
  }
}
```

### 创建成品
```http
POST /products
Content-Type: application/json

{
  "name": "产品B",
  "model": "PB-001",
  "category": "机械",
  "price": 18000.00,
  "stock": 10,
  "minStock": 1,
  "materials": [
    {
      "materialId": 1,
      "quantity": 3
    },
    {
      "materialId": 2,
      "quantity": 1
    }
  ],
  "description": "高精度机械产品"
}
```

## 供应商管理

### 获取供应商列表
```http
GET /suppliers?page=1&limit=10
```

**响应：**
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": 1,
        "name": "供应商A",
        "contact": "张三",
        "phone": "13800138000",
        "email": "<EMAIL>",
        "address": "北京市朝阳区",
        "status": "active",
        "createdAt": "2024-01-01T00:00:00.000Z"
      }
    ],
    "total": 15,
    "page": 1,
    "limit": 10
  }
}
```

### 创建供应商
```http
POST /suppliers
Content-Type: application/json

{
  "name": "供应商B",
  "contact": "李四",
  "phone": "13900139000",
  "email": "<EMAIL>",
  "address": "上海市浦东新区",
  "description": "专业钢材供应商"
}
```

## 错误处理

### 标准错误响应
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "输入数据验证失败",
    "details": {
      "field": "name",
      "message": "名称不能为空"
    }
  }
}
```

### 常见错误码
- `400` - 请求参数错误
- `401` - 未认证或token无效
- `403` - 权限不足
- `404` - 资源不存在
- `409` - 资源冲突（如重复创建）
- `500` - 服务器内部错误

## 数据类型说明

### 分页响应格式
```typescript
interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  limit: number
  totalPages?: number
}
```

### 标准响应格式
```typescript
interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: {
    code: string
    message: string
    details?: any
  }
}
```

## 使用示例

### JavaScript/TypeScript
```javascript
// 获取原材料列表
const response = await fetch('/api/materials?page=1&limit=10', {
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  }
})
const data = await response.json()

// 创建原材料
const newMaterial = await fetch('/api/materials', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    name: '新材料',
    category: '金属',
    unit: '吨',
    price: 6000.00,
    stock: 30,
    minStock: 3
  })
})
```

### cURL
```bash
# 登录
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"password"}'

# 获取原材料列表
curl -X GET "http://localhost:3000/api/materials?page=1&limit=10" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 创建原材料
curl -X POST http://localhost:3000/api/materials \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"name":"新材料","category":"金属","unit":"吨","price":6000.00,"stock":30,"minStock":3}'
```

## 注意事项

1. **缓存机制**: 列表查询接口已启用前端缓存，缓存时间为5分钟
2. **并发控制**: 同一资源的并发修改可能导致冲突，建议使用乐观锁
3. **数据验证**: 所有输入数据都会进行服务端验证
4. **日志记录**: 所有API调用都会记录到系统日志中
5. **错误恢复**: 系统具备基础的错误恢复机制

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 支持原材料、成品、供应商基础管理
- 集成健康检查和错误边界
- 添加前端缓存机制

---

**技术支持**: 如有问题请联系系统管理员
**最后更新**: 2024-01-01
