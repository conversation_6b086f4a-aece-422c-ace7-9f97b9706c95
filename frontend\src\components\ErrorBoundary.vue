<template>
  <div v-if="hasError" class="error-boundary">
    <div class="error-content">
      <div class="error-icon">
        <el-icon :size="48" color="#f56c6c">
          <WarningFilled />
        </el-icon>
      </div>
      
      <h3 class="error-title">组件加载失败</h3>
      
      <p class="error-message">
        {{ errorMessage || '抱歉，页面遇到了一些问题。请尝试刷新页面或联系管理员。' }}
      </p>
      
      <div class="error-actions">
        <el-button type="primary" @click="retry">
          <el-icon><Refresh /></el-icon>
          重试
        </el-button>
        
        <el-button @click="goHome">
          <el-icon><HomeFilled /></el-icon>
          返回首页
        </el-button>
        
        <el-button 
          v-if="showDetails" 
          type="info" 
          text 
          @click="toggleDetails"
        >
          {{ showErrorDetails ? '隐藏详情' : '显示详情' }}
        </el-button>
      </div>
      
      <div v-if="showDetails && showErrorDetails" class="error-details">
        <el-collapse>
          <el-collapse-item title="错误详情" name="error">
            <pre class="error-stack">{{ errorDetails }}</pre>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>
  </div>
  
  <slot v-else />
</template>

<script setup lang="ts">
import { ref, onErrorCaptured, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { WarningFilled, Refresh, HomeFilled } from '@element-plus/icons-vue'

interface Props {
  // 是否显示错误详情按钮（开发环境默认显示）
  showDetails?: boolean
  // 自定义错误消息
  fallbackMessage?: string
  // 错误恢复回调
  onError?: (error: Error, instance: any) => void
  // 重试回调
  onRetry?: () => void
}

const props = withDefaults(defineProps<Props>(), {
  showDetails: import.meta.env.DEV,
  fallbackMessage: '',
  onError: undefined,
  onRetry: undefined
})

const emit = defineEmits<{
  error: [error: Error, instance: any]
  retry: []
}>()

const router = useRouter()

// 错误状态
const hasError = ref(false)
const errorMessage = ref('')
const errorDetails = ref('')
const showErrorDetails = ref(false)

// 错误捕获
onErrorCaptured((error: Error, instance: any, info: string) => {
  console.error('ErrorBoundary 捕获到错误:', error)
  console.error('错误信息:', info)
  console.error('组件实例:', instance)
  
  hasError.value = true
  errorMessage.value = props.fallbackMessage || error.message || '未知错误'
  errorDetails.value = `错误: ${error.message}\n\n堆栈信息:\n${error.stack}\n\n组件信息: ${info}`
  
  // 触发错误事件
  emit('error', error, instance)
  
  // 调用自定义错误处理
  if (props.onError) {
    props.onError(error, instance)
  }
  
  // 错误上报（生产环境）
  if (import.meta.env.PROD) {
    reportError(error, info, instance)
  }
  
  // 阻止错误继续向上传播
  return false
})

// 重试功能
const retry = async () => {
  try {
    hasError.value = false
    errorMessage.value = ''
    errorDetails.value = ''
    showErrorDetails.value = false
    
    // 等待下一个tick确保DOM更新
    await nextTick()
    
    // 触发重试事件
    emit('retry')
    
    // 调用自定义重试回调
    if (props.onRetry) {
      props.onRetry()
    }
    
    ElMessage.success('重试成功')
  } catch (error) {
    console.error('重试失败:', error)
    ElMessage.error('重试失败，请刷新页面')
  }
}

// 返回首页
const goHome = () => {
  router.push('/')
}

// 切换错误详情显示
const toggleDetails = () => {
  showErrorDetails.value = !showErrorDetails.value
}

// 错误上报函数
const reportError = (error: Error, info: string, instance: any) => {
  // 收集错误信息
  const errorReport = {
    message: error.message,
    stack: error.stack,
    componentInfo: info,
    url: window.location.href,
    userAgent: navigator.userAgent,
    timestamp: new Date().toISOString(),
    userId: localStorage.getItem('userId') || 'anonymous'
  }
  
  // 发送到错误收集服务（这里可以集成第三方服务）
  console.log('错误报告:', errorReport)
  
  // 可以发送到后端API
  // fetch('/api/errors', {
  //   method: 'POST',
  //   headers: { 'Content-Type': 'application/json' },
  //   body: JSON.stringify(errorReport)
  // }).catch(console.error)
}

// 暴露方法供父组件调用
defineExpose({
  retry,
  hasError: () => hasError.value,
  clearError: () => {
    hasError.value = false
    errorMessage.value = ''
    errorDetails.value = ''
  }
})
</script>

<style scoped>
.error-boundary {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 20px;
  background-color: #fafafa;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.error-content {
  text-align: center;
  max-width: 500px;
}

.error-icon {
  margin-bottom: 16px;
}

.error-title {
  color: #303133;
  font-size: 20px;
  font-weight: 500;
  margin: 0 0 12px 0;
}

.error-message {
  color: #606266;
  font-size: 14px;
  line-height: 1.6;
  margin: 0 0 24px 0;
}

.error-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
  margin-bottom: 20px;
}

.error-details {
  text-align: left;
  margin-top: 20px;
}

.error-stack {
  background-color: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  font-size: 12px;
  line-height: 1.4;
  color: #666;
  white-space: pre-wrap;
  word-break: break-all;
  max-height: 300px;
  overflow-y: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .error-boundary {
    min-height: 300px;
    padding: 16px;
  }
  
  .error-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .error-actions .el-button {
    width: 100%;
    max-width: 200px;
  }
}
</style>
