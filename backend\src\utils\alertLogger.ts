/**
 * 基础日志告警系统
 * 为30人工厂提供简单实用的系统告警功能
 */

import * as fs from 'fs'
import * as path from 'path'

export interface AlertConfig {
  // 日志文件路径
  logPath: string
  
  // 是否启用控制台输出
  enableConsole: boolean
  
  // 最大日志文件大小（字节）
  maxFileSize: number
  
  // 保留的日志文件数量
  maxFiles: number
  
  // 告警级别
  level: 'error' | 'warning' | 'info'
}

export interface AlertMessage {
  timestamp: string
  level: 'error' | 'warning' | 'info'
  category: string
  message: string
  details?: any
  source?: string
}

class AlertLogger {
  private config: AlertConfig
  private logStream: fs.WriteStream | null = null

  constructor(config: Partial<AlertConfig> = {}) {
    this.config = {
      logPath: path.join(process.cwd(), 'logs', 'alerts.log'),
      enableConsole: true,
      maxFileSize: 10 * 1024 * 1024, // 10MB
      maxFiles: 5,
      level: 'warning',
      ...config
    }

    this.initializeLogger()
  }

  /**
   * 初始化日志记录器
   */
  private initializeLogger() {
    try {
      // 确保日志目录存在
      const logDir = path.dirname(this.config.logPath)
      if (!fs.existsSync(logDir)) {
        fs.mkdirSync(logDir, { recursive: true })
      }

      // 检查并轮转日志文件
      this.rotateLogIfNeeded()

      // 创建写入流
      this.logStream = fs.createWriteStream(this.config.logPath, { flags: 'a' })
      
      console.log(`告警日志系统已启动，日志文件: ${this.config.logPath}`)
    } catch (error) {
      console.error('初始化告警日志系统失败:', error)
    }
  }

  /**
   * 记录错误告警
   */
  error(category: string, message: string, details?: any, source?: string) {
    this.log({
      timestamp: new Date().toISOString(),
      level: 'error',
      category,
      message,
      details,
      source
    })
  }

  /**
   * 记录警告告警
   */
  warning(category: string, message: string, details?: any, source?: string) {
    this.log({
      timestamp: new Date().toISOString(),
      level: 'warning',
      category,
      message,
      details,
      source
    })
  }

  /**
   * 记录信息告警
   */
  info(category: string, message: string, details?: any, source?: string) {
    this.log({
      timestamp: new Date().toISOString(),
      level: 'info',
      category,
      message,
      details,
      source
    })
  }

  /**
   * 记录告警消息
   */
  private log(alert: AlertMessage) {
    try {
      // 检查告警级别
      if (!this.shouldLog(alert.level)) {
        return
      }

      // 格式化日志消息
      const logMessage = this.formatLogMessage(alert)

      // 写入文件
      if (this.logStream) {
        this.logStream.write(logMessage + '\n')
      }

      // 控制台输出
      if (this.config.enableConsole) {
        this.outputToConsole(alert, logMessage)
      }

      // 检查是否需要轮转日志
      this.rotateLogIfNeeded()

    } catch (error) {
      console.error('写入告警日志失败:', error)
    }
  }

  /**
   * 检查是否应该记录此级别的日志
   */
  private shouldLog(level: AlertMessage['level']): boolean {
    const levels = { info: 0, warning: 1, error: 2 }
    return levels[level] >= levels[this.config.level]
  }

  /**
   * 格式化日志消息
   */
  private formatLogMessage(alert: AlertMessage): string {
    const parts = [
      alert.timestamp,
      `[${alert.level.toUpperCase()}]`,
      `[${alert.category}]`,
      alert.message
    ]

    if (alert.source) {
      parts.push(`(来源: ${alert.source})`)
    }

    let logMessage = parts.join(' ')

    if (alert.details) {
      logMessage += '\n详情: ' + JSON.stringify(alert.details, null, 2)
    }

    return logMessage
  }

  /**
   * 输出到控制台
   */
  private outputToConsole(alert: AlertMessage, logMessage: string) {
    switch (alert.level) {
      case 'error':
        console.error('🚨', logMessage)
        break
      case 'warning':
        console.warn('⚠️', logMessage)
        break
      case 'info':
        console.info('ℹ️', logMessage)
        break
    }
  }

  /**
   * 检查并轮转日志文件
   */
  private rotateLogIfNeeded() {
    try {
      if (!fs.existsSync(this.config.logPath)) {
        return
      }

      const stats = fs.statSync(this.config.logPath)
      if (stats.size >= this.config.maxFileSize) {
        this.rotateLog()
      }
    } catch (error) {
      console.error('检查日志文件大小失败:', error)
    }
  }

  /**
   * 轮转日志文件
   */
  private rotateLog() {
    try {
      // 关闭当前写入流
      if (this.logStream) {
        this.logStream.end()
        this.logStream = null
      }

      const logDir = path.dirname(this.config.logPath)
      const logName = path.basename(this.config.logPath, '.log')
      const logExt = '.log'

      // 移动现有的日志文件
      for (let i = this.config.maxFiles - 1; i >= 1; i--) {
        const oldFile = path.join(logDir, `${logName}.${i}${logExt}`)
        const newFile = path.join(logDir, `${logName}.${i + 1}${logExt}`)
        
        if (fs.existsSync(oldFile)) {
          if (i === this.config.maxFiles - 1) {
            // 删除最老的文件
            fs.unlinkSync(oldFile)
          } else {
            fs.renameSync(oldFile, newFile)
          }
        }
      }

      // 移动当前日志文件
      const firstRotatedFile = path.join(logDir, `${logName}.1${logExt}`)
      if (fs.existsSync(this.config.logPath)) {
        fs.renameSync(this.config.logPath, firstRotatedFile)
      }

      // 重新创建写入流
      this.logStream = fs.createWriteStream(this.config.logPath, { flags: 'a' })
      
      console.log('日志文件已轮转')
    } catch (error) {
      console.error('日志文件轮转失败:', error)
    }
  }

  /**
   * 获取最近的告警记录
   */
  getRecentAlerts(count: number = 50): AlertMessage[] {
    try {
      if (!fs.existsSync(this.config.logPath)) {
        return []
      }

      const content = fs.readFileSync(this.config.logPath, 'utf-8')
      const lines = content.trim().split('\n').filter(line => line.trim())
      
      // 简单解析最近的日志行
      const alerts: AlertMessage[] = []
      const recentLines = lines.slice(-count)

      for (const line of recentLines) {
        try {
          const alert = this.parseLogLine(line)
          if (alert) {
            alerts.push(alert)
          }
        } catch (error) {
          // 忽略解析失败的行
        }
      }

      return alerts.reverse() // 最新的在前面
    } catch (error) {
      console.error('读取告警记录失败:', error)
      return []
    }
  }

  /**
   * 解析日志行
   */
  private parseLogLine(line: string): AlertMessage | null {
    try {
      // 简单的正则解析：timestamp [LEVEL] [category] message
      const match = line.match(/^(\S+)\s+\[(\w+)\]\s+\[([^\]]+)\]\s+(.+)/)
      if (!match) return null

      const [, timestamp, level, category, message] = match
      
      return {
        timestamp,
        level: level.toLowerCase() as AlertMessage['level'],
        category,
        message
      }
    } catch (error) {
      return null
    }
  }

  /**
   * 清理旧的告警记录
   */
  cleanup() {
    try {
      if (this.logStream) {
        this.logStream.end()
        this.logStream = null
      }
    } catch (error) {
      console.error('清理告警日志系统失败:', error)
    }
  }
}

// 创建全局告警日志实例
export const alertLogger = new AlertLogger({
  level: 'warning', // 30人工厂，只记录警告和错误
  enableConsole: process.env.NODE_ENV !== 'production'
})

// 便捷的告警函数
export const alertError = (category: string, message: string, details?: any, source?: string) => {
  alertLogger.error(category, message, details, source)
}

export const alertWarning = (category: string, message: string, details?: any, source?: string) => {
  alertLogger.warning(category, message, details, source)
}

export const alertInfo = (category: string, message: string, details?: any, source?: string) => {
  alertLogger.info(category, message, details, source)
}

// 进程退出时清理
process.on('exit', () => {
  alertLogger.cleanup()
})

process.on('SIGINT', () => {
  alertLogger.cleanup()
  process.exit(0)
})

process.on('SIGTERM', () => {
  alertLogger.cleanup()
  process.exit(0)
})
