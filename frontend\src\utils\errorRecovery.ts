/**
 * 错误恢复工具类
 * 提供各种错误恢复策略和机制
 */

import { ElMessage, ElMessageBox } from 'element-plus'
import { nextTick } from 'vue'
import router from '@/router'

export interface ErrorRecoveryOptions {
  // 最大重试次数
  maxRetries?: number
  // 重试延迟（毫秒）
  retryDelay?: number
  // 是否显示用户提示
  showUserMessage?: boolean
  // 自定义错误消息
  customMessage?: string
  // 是否自动重试
  autoRetry?: boolean
  // 重试前的回调
  beforeRetry?: () => Promise<void> | void
  // 重试失败的回调
  onRetryFailed?: (error: Error) => void
}

export class ErrorRecoveryManager {
  private retryCount = 0
  private readonly options: Required<ErrorRecoveryOptions>

  constructor(options: ErrorRecoveryOptions = {}) {
    this.options = {
      maxRetries: 3,
      retryDelay: 1000,
      showUserMessage: true,
      customMessage: '',
      autoRetry: false,
      beforeRetry: () => {},
      onRetryFailed: () => {},
      ...options
    }
  }

  /**
   * 执行带重试的异步操作
   */
  async executeWithRetry<T>(
    operation: () => Promise<T>,
    context?: string
  ): Promise<T> {
    this.retryCount = 0

    while (this.retryCount <= this.options.maxRetries) {
      try {
        // 执行重试前的回调
        if (this.retryCount > 0) {
          await this.options.beforeRetry()
          await this.delay(this.options.retryDelay)
        }

        const result = await operation()
        
        // 成功后重置重试计数
        if (this.retryCount > 0) {
          this.showSuccessMessage('操作成功')
        }
        
        return result
      } catch (error) {
        this.retryCount++
        console.error(`操作失败 (第${this.retryCount}次尝试):`, error)

        // 如果达到最大重试次数
        if (this.retryCount > this.options.maxRetries) {
          this.options.onRetryFailed(error as Error)
          throw new Error(`操作失败，已重试${this.options.maxRetries}次: ${(error as Error).message}`)
        }

        // 显示重试提示
        if (this.options.showUserMessage && !this.options.autoRetry) {
          const shouldRetry = await this.showRetryDialog(
            error as Error,
            this.retryCount,
            context
          )
          
          if (!shouldRetry) {
            throw error
          }
        } else if (this.options.showUserMessage) {
          this.showRetryMessage(this.retryCount, this.options.maxRetries)
        }
      }
    }

    throw new Error('意外的执行路径')
  }

  /**
   * 显示重试对话框
   */
  private async showRetryDialog(
    error: Error,
    retryCount: number,
    context?: string
  ): Promise<boolean> {
    try {
      await ElMessageBox.confirm(
        `${context || '操作'}失败: ${error.message}\n\n是否重试？(第${retryCount}/${this.options.maxRetries}次)`,
        '操作失败',
        {
          confirmButtonText: '重试',
          cancelButtonText: '取消',
          type: 'warning',
          center: true
        }
      )
      return true
    } catch {
      return false
    }
  }

  /**
   * 显示重试消息
   */
  private showRetryMessage(retryCount: number, maxRetries: number) {
    ElMessage.warning(`操作失败，正在重试... (${retryCount}/${maxRetries})`)
  }

  /**
   * 显示成功消息
   */
  private showSuccessMessage(message: string) {
    ElMessage.success(message)
  }

  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * 重置重试计数
   */
  reset() {
    this.retryCount = 0
  }
}

/**
 * 网络请求错误恢复
 */
export class NetworkErrorRecovery extends ErrorRecoveryManager {
  constructor() {
    super({
      maxRetries: 3,
      retryDelay: 2000,
      showUserMessage: true,
      autoRetry: true
    })
  }

  /**
   * 处理API请求错误
   */
  async handleApiError<T>(
    apiCall: () => Promise<T>,
    endpoint?: string
  ): Promise<T> {
    return this.executeWithRetry(apiCall, `API请求${endpoint ? ` (${endpoint})` : ''}`)
  }
}

/**
 * 组件错误恢复
 */
export class ComponentErrorRecovery {
  /**
   * 重新渲染组件
   */
  static async reRenderComponent(componentRef: any): Promise<void> {
    if (!componentRef) return

    try {
      // 强制重新渲染
      await nextTick()
      
      if (componentRef.value && typeof componentRef.value.$forceUpdate === 'function') {
        componentRef.value.$forceUpdate()
      }
      
      ElMessage.success('组件已重新加载')
    } catch (error) {
      console.error('组件重新渲染失败:', error)
      ElMessage.error('组件重新加载失败')
    }
  }

  /**
   * 重置组件状态
   */
  static resetComponentState(componentRef: any, initialState: any): void {
    if (!componentRef || !componentRef.value) return

    try {
      Object.assign(componentRef.value, initialState)
      ElMessage.success('组件状态已重置')
    } catch (error) {
      console.error('组件状态重置失败:', error)
      ElMessage.error('组件状态重置失败')
    }
  }
}

/**
 * 路由错误恢复
 */
export class RouteErrorRecovery {
  /**
   * 安全导航到指定路由
   */
  static async safeNavigate(to: string, fallback = '/'): Promise<void> {
    try {
      await router.push(to)
    } catch (error) {
      console.error('路由导航失败:', error)
      
      try {
        await router.push(fallback)
        ElMessage.warning('页面跳转失败，已返回首页')
      } catch (fallbackError) {
        console.error('回退路由也失败:', fallbackError)
        window.location.href = fallback
      }
    }
  }

  /**
   * 刷新当前路由
   */
  static async refreshCurrentRoute(): Promise<void> {
    try {
      const currentRoute = router.currentRoute.value
      await router.replace({ path: '/redirect' + currentRoute.fullPath })
    } catch (error) {
      console.error('路由刷新失败:', error)
      window.location.reload()
    }
  }
}

/**
 * 数据错误恢复
 */
export class DataErrorRecovery {
  /**
   * 重新加载数据
   */
  static async reloadData<T>(
    dataLoader: () => Promise<T>,
    context?: string
  ): Promise<T | null> {
    const recovery = new ErrorRecoveryManager({
      maxRetries: 2,
      retryDelay: 1500,
      showUserMessage: true
    })

    try {
      return await recovery.executeWithRetry(dataLoader, context || '数据加载')
    } catch (error) {
      console.error('数据重新加载失败:', error)
      ElMessage.error('数据加载失败，请刷新页面重试')
      return null
    }
  }

  /**
   * 清除缓存并重新加载
   */
  static async clearCacheAndReload<T>(
    cacheKey: string,
    dataLoader: () => Promise<T>
  ): Promise<T | null> {
    try {
      // 清除相关缓存
      localStorage.removeItem(cacheKey)
      sessionStorage.removeItem(cacheKey)
      
      // 重新加载数据
      return await this.reloadData(dataLoader, '清除缓存后的数据加载')
    } catch (error) {
      console.error('清除缓存并重新加载失败:', error)
      return null
    }
  }
}

// 导出默认实例
export const networkRecovery = new NetworkErrorRecovery()
export const defaultRecovery = new ErrorRecoveryManager()

// 全局错误恢复函数
export const globalErrorRecovery = {
  /**
   * 处理未捕获的错误
   */
  handleUncaughtError(error: Error, context?: string) {
    console.error('未捕获的错误:', error)
    
    ElMessage.error({
      message: `系统错误: ${error.message}`,
      duration: 5000,
      showClose: true
    })

    // 错误上报
    this.reportError(error, context)
  },

  /**
   * 错误上报
   */
  reportError(error: Error, context?: string) {
    const errorReport = {
      message: error.message,
      stack: error.stack,
      context,
      url: window.location.href,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent
    }

    console.log('错误报告:', errorReport)
    
    // 这里可以发送到错误收集服务
    // 例如：Sentry、LogRocket 等
  }
}
