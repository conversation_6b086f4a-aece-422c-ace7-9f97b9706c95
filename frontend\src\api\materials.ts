import type { ApiResponse } from './auth'
import { cachedApi, CachePresets, generateCacheKey } from './cachedApi'
import api from './index'

// 原材料相关类型定义
export interface Material {
  id: number
  code: string
  name: string
  specification?: string
  unit: string
  cost_price: number
  stock_min: number
  stock_max: number
  current_stock: number
  status: 'active' | 'inactive'
  created_at: string
  updated_at: string
}

export interface MaterialForm {
  code: string
  name: string
  specification?: string
  unit: string
  cost_price?: number
  stock_min?: number
  stock_max?: number
  current_stock?: number
}

export interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  limit: number
  totalPages: number
}

export interface MaterialQuery {
  page?: number
  limit?: number
  search?: string
}

// 原材料API
export const materialApi = {
  // 获取原材料列表（带缓存）
  getMaterials(params?: MaterialQuery): Promise<ApiResponse<PaginatedResponse<Material>>> {
    const cacheKey = generateCacheKey('materials', params)
    return cachedApi.get(
      () => api.get('/materials', { params }),
      cacheKey,
      CachePresets.MEDIUM
    )
  },

  // 获取单个原材料（带缓存）
  getMaterial(id: number): Promise<ApiResponse<Material>> {
    const cacheKey = `material_${id}`
    return cachedApi.get(
      () => api.get(`/materials/${id}`),
      cacheKey,
      CachePresets.LONG
    )
  },

  // 创建原材料（清除相关缓存）
  async createMaterial(data: MaterialForm): Promise<ApiResponse> {
    const response = await api.post('/materials', data)
    if (response.success) {
      // 清除列表缓存
      cachedApi.invalidatePattern('materials')
    }
    return response
  },

  // 更新原材料（清除相关缓存）
  async updateMaterial(id: number, data: Partial<MaterialForm>): Promise<ApiResponse> {
    const response = await api.put(`/materials/${id}`, data)
    if (response.success) {
      // 清除相关缓存
      cachedApi.invalidate(`material_${id}`)
      cachedApi.invalidatePattern('materials')
    }
    return response
  },

  // 删除原材料（清除相关缓存）
  async deleteMaterial(id: number): Promise<ApiResponse> {
    const response = await api.delete(`/materials/${id}`)
    if (response.success) {
      // 清除相关缓存
      cachedApi.invalidate(`material_${id}`)
      cachedApi.invalidatePattern('materials')
    }
    return response
  }
}
