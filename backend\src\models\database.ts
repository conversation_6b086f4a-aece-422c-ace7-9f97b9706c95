import path from 'path';
import sqlite3 from 'sqlite3';
import { alertError } from '../utils/alertLogger';
import {
    createCustomerTables,
    createDefaultUser,
    createInventoryIndexes,
    createInventoryTables,
    createMaterialIndexes,
    createMaterialTables,
    createOrderIndexes,
    createOrderTables,
    createProductIndexes,
    createProductionIndexes,
    createProductionTables,
    createProductTables,
    createSupplierTables,
    createUserTables
} from './index';

// 数据库连接配置接口
interface DatabaseConfig {
  path: string;
  retryMaxAttempts: number;
  retryBaseDelay: number;
  connectionTimeout: number;
  healthCheckInterval: number;
}

// 连接状态枚举
enum ConnectionStatus {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  ERROR = 'error'
}

// 连接统计信息
interface ConnectionStats {
  status: ConnectionStatus;
  retryCount: number;
  lastError?: string;
  lastConnectedAt?: Date;
  lastHealthCheckAt?: Date;
}

// 数据库实例和状态
let db: sqlite3.Database | null = null;
let connectionStats: ConnectionStats = {
  status: ConnectionStatus.DISCONNECTED,
  retryCount: 0
};
let healthCheckTimer: NodeJS.Timeout | null = null;

// 获取数据库配置
export function getDatabaseConfig(): DatabaseConfig {
  return {
    path: process.env.DATABASE_PATH || path.join(__dirname, '../../data/erp.db'),
    retryMaxAttempts: parseInt(process.env.DB_RETRY_MAX_ATTEMPTS || '5'),
    retryBaseDelay: parseInt(process.env.DB_RETRY_BASE_DELAY || '250'),
    connectionTimeout: parseInt(process.env.DB_CONNECTION_TIMEOUT || '10000'),
    healthCheckInterval: parseInt(process.env.DB_HEALTH_CHECK_INTERVAL || '30000')
  };
}

// 延迟函数
function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// 数据库错误类型
enum DatabaseErrorType {
  CONNECTION_FAILED = 'CONNECTION_FAILED',
  RETRY_EXHAUSTED = 'RETRY_EXHAUSTED',
  HEALTH_CHECK_FAILED = 'HEALTH_CHECK_FAILED',
  RECONNECTION_FAILED = 'RECONNECTION_FAILED'
}

// 检查错误是否可重试
export function isRetryableError(error: any): boolean {
  const retryableErrors = [
    'SQLITE_BUSY',      // 数据库忙
    'SQLITE_LOCKED',    // 数据库被锁定
    'SQLITE_IOERR',     // I/O错误
    'ENOENT',           // 文件不存在（可能是临时的）
    'EACCES',           // 权限错误（可能是临时的）
    'ETIMEDOUT',        // 超时错误
    'ECONNRESET'        // 连接重置
  ];

  const errorMessage = error.message?.toLowerCase() || '';
  const errorCode = error.code?.toLowerCase() || '';

  return retryableErrors.some(errCode =>
    errorMessage.includes(errCode.toLowerCase()) ||
    errorCode.includes(errCode.toLowerCase())
  );
}

// 创建数据库错误
function createDatabaseError(type: DatabaseErrorType, message: string, originalError?: any): Error {
  const error = new Error(message);
  error.name = type;
  if (originalError) {
    (error as any).originalError = originalError;
    (error as any).stack = originalError.stack;
  }
  return error;
}

// 日志记录函数
function logDatabaseEvent(level: 'info' | 'warn' | 'error', message: string, details?: any): void {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] [DATABASE] ${message}`;

  switch (level) {
    case 'info':
      console.log(logMessage, details ? JSON.stringify(details, null, 2) : '');
      break;
    case 'warn':
      console.warn(logMessage, details ? JSON.stringify(details, null, 2) : '');
      break;
    case 'error':
      console.error(logMessage, details ? JSON.stringify(details, null, 2) : '');
      break;
  }
}

// 带重试的数据库连接函数
async function connectWithRetry(config: DatabaseConfig): Promise<sqlite3.Database> {
  let lastError: any;

  for (let attempt = 1; attempt <= config.retryMaxAttempts; attempt++) {
    try {
      connectionStats.status = ConnectionStatus.CONNECTING;
      connectionStats.retryCount = attempt - 1;

      logDatabaseEvent('info', `数据库连接尝试 ${attempt}/${config.retryMaxAttempts}`, {
        path: config.path,
        attempt,
        maxAttempts: config.retryMaxAttempts
      });

      // 确保数据目录存在
      const fs = require('fs');
      const dataDir = path.dirname(config.path);
      if (!fs.existsSync(dataDir)) {
        fs.mkdirSync(dataDir, { recursive: true });
      }

      // 创建数据库连接
      const database = await new Promise<sqlite3.Database>((resolve, reject) => {
        const dbInstance = new sqlite3.Database(config.path, (err) => {
          if (err) {
            reject(err);
          } else {
            resolve(dbInstance);
          }
        });
      });

      // 设置UTF-8编码
      await new Promise<void>((resolve, reject) => {
        database.run("PRAGMA encoding = 'UTF-8'", (err) => {
          if (err) {
            console.error('设置编码失败:', err);
            reject(err);
          } else {
            resolve();
          }
        });
      });

      // 连接成功
      connectionStats.status = ConnectionStatus.CONNECTED;
      connectionStats.lastConnectedAt = new Date();
      connectionStats.lastError = undefined;

      logDatabaseEvent('info', '数据库连接成功', {
        path: config.path,
        attempt,
        totalRetries: attempt - 1
      });
      return database;

    } catch (error: any) {
      lastError = error;
      connectionStats.lastError = error?.message || String(error);

      const isRetryable = isRetryableError(error);
      const isLastAttempt = attempt === config.retryMaxAttempts;

      logDatabaseEvent('error', `数据库连接失败 (尝试 ${attempt}/${config.retryMaxAttempts})`, {
        error: error?.message || String(error),
        isRetryable,
        isLastAttempt,
        errorCode: error?.code
      });

      // 如果是最后一次尝试或错误不可重试，直接抛出错误
      if (isLastAttempt || !isRetryable) {
        connectionStats.status = ConnectionStatus.ERROR;

        if (isLastAttempt) {
          throw createDatabaseError(
            DatabaseErrorType.RETRY_EXHAUSTED,
            `数据库连接失败，已重试 ${config.retryMaxAttempts} 次`,
            error
          );
        } else {
          throw createDatabaseError(
            DatabaseErrorType.CONNECTION_FAILED,
            `数据库连接失败，错误不可重试: ${error?.message || String(error)}`,
            error
          );
        }
      }

      // 计算指数退避延迟时间
      const delayMs = config.retryBaseDelay * Math.pow(2, attempt - 1);
      logDatabaseEvent('warn', `等待 ${delayMs}ms 后重试...`, { delayMs, nextAttempt: attempt + 1 });

      await delay(delayMs);
    }
  }

  // 如果所有重试都失败了
  connectionStats.status = ConnectionStatus.ERROR;
  throw lastError;
}

// 获取连接状态
export function getConnectionStatus(): ConnectionStats {
  return { ...connectionStats };
}

// 重置连接状态
function resetConnectionStats(): void {
  connectionStats = {
    status: ConnectionStatus.DISCONNECTED,
    retryCount: 0,
    lastError: undefined,
    lastConnectedAt: undefined,
    lastHealthCheckAt: undefined
  };
}

// 健康检查函数
async function healthCheck(): Promise<boolean> {
  if (!db) {
    return false;
  }

  try {
    await new Promise<void>((resolve, reject) => {
      db!.get('SELECT 1 as test', (err, row) => {
        if (err) {
          reject(err);
        } else {
          resolve();
        }
      });
    });

    connectionStats.lastHealthCheckAt = new Date();
    if (connectionStats.status !== ConnectionStatus.CONNECTED) {
      connectionStats.status = ConnectionStatus.CONNECTED;
      console.log('数据库连接健康检查通过');
    }

    return true;
  } catch (error: any) {
    const errorMessage = error?.message || String(error);
    console.error('数据库健康检查失败:', errorMessage);

    // 记录告警
    alertError('DATABASE', '数据库健康检查失败', { error: errorMessage }, 'Database');

    connectionStats.status = ConnectionStatus.ERROR;
    connectionStats.lastError = errorMessage;
    return false;
  }
}

// 启动健康检查定时器
function startHealthCheck(config: DatabaseConfig): void {
  if (healthCheckTimer) {
    clearInterval(healthCheckTimer);
  }

  healthCheckTimer = setInterval(async () => {
    const isHealthy = await healthCheck();

    if (!isHealthy && connectionStats.status === ConnectionStatus.ERROR) {
      console.log('检测到数据库连接异常，尝试重新连接...');
      try {
        await reconnectDatabase();
      } catch (error: any) {
        const errorMessage = error?.message || String(error);
        console.error('自动重连失败:', errorMessage);

        // 记录告警
        alertError('DATABASE', '数据库自动重连失败', { error: errorMessage }, 'Database');
      }
    }
  }, config.healthCheckInterval);

  console.log(`数据库健康检查已启动，间隔: ${config.healthCheckInterval}ms`);
}

// 停止健康检查定时器
function stopHealthCheck(): void {
  if (healthCheckTimer) {
    clearInterval(healthCheckTimer);
    healthCheckTimer = null;
    console.log('数据库健康检查已停止');
  }
}



// 获取数据库实例
export function getDatabase(): sqlite3.Database {
  if (!db) {
    throw new Error('数据库未初始化');
  }
  return db;
}

// 初始化数据库
export async function initDatabase(): Promise<void> {
  try {
    const config = getDatabaseConfig();

    logDatabaseEvent('info', '开始初始化数据库', {
      path: config.path,
      retryConfig: {
        maxAttempts: config.retryMaxAttempts,
        baseDelay: config.retryBaseDelay,
        healthCheckInterval: config.healthCheckInterval
      }
    });

    // 重置连接状态
    resetConnectionStats();

    // 使用重试机制连接数据库
    db = await connectWithRetry(config);

    // 创建数据表
    await createTables();

    // 启动健康检查
    startHealthCheck(config);

    logDatabaseEvent('info', '数据库初始化完成');

  } catch (error: any) {
    logDatabaseEvent('error', '数据库初始化失败', {
      error: error?.message || String(error),
      stack: error?.stack
    });
    throw error;
  }
}

// 创建数据表
async function createTables(): Promise<void> {
  if (!db) {
    throw new Error('数据库实例为空，无法创建表');
  }

  try {
    // 按顺序创建各模块的表
    await createUserTables(db);
    await createSupplierTables(db);
    await createCustomerTables(db);
    await createMaterialTables(db);
    await createProductTables(db);
    await createOrderTables(db);
    await createProductionTables(db);
    await createInventoryTables(db);

    // 创建索引
    await createMaterialIndexes(db);
    await createProductIndexes(db);
    await createOrderIndexes(db);
    await createProductionIndexes(db);
    await createInventoryIndexes(db);

    console.log('数据表和索引创建成功');

    // 创建默认用户
    await createDefaultUser(db);
  } catch (error: any) {
    const errorMessage = error?.message || String(error);
    console.error('创建数据表失败:', errorMessage);

    // 记录告警
    alertError('DATABASE', '创建数据表失败', { error: errorMessage }, 'Database');

    throw error;
  }
}

// 关闭数据库连接
export function closeDatabase(): void {
  logDatabaseEvent('info', '开始关闭数据库连接');

  // 停止健康检查
  stopHealthCheck();

  if (db) {
    db.close((err) => {
      if (err) {
        logDatabaseEvent('error', '关闭数据库时出错', { error: err.message });
      } else {
        logDatabaseEvent('info', '数据库连接已关闭');
      }
    });

    // 重置状态
    db = null;
    resetConnectionStats();
  }
}

// 手动重连数据库（导出供外部使用）
export async function reconnectDatabase(): Promise<void> {
  logDatabaseEvent('info', '手动重连数据库');

  const config = getDatabaseConfig();

  // 关闭现有连接
  if (db) {
    db.close();
    db = null;
  }

  // 停止健康检查
  stopHealthCheck();

  try {
    // 重新连接
    db = await connectWithRetry(config);

    // 重新创建表（如果需要）
    await createTables();

    // 重新启动健康检查
    startHealthCheck(config);

    logDatabaseEvent('info', '手动重连成功');
  } catch (error: any) {
    logDatabaseEvent('error', '手动重连失败', { error: error?.message || String(error) });
    throw error;
  }
}
