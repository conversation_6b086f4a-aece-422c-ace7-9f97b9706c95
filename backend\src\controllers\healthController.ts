/**
 * 健康检查控制器
 * 为30人工厂提供简单实用的系统监控功能
 */

import { Request, Response } from 'express'
import * as fs from 'fs'
import * as os from 'os'
import * as path from 'path'
import { getDatabase } from '../models/database'

interface HealthStatus {
  status: 'healthy' | 'degraded' | 'unhealthy'
  timestamp: string
  uptime: number
  version: string
  environment: string
  database: {
    status: 'connected' | 'disconnected' | 'error'
    responseTime?: number
    error?: string
  }
  system: {
    memory: {
      used: string
      total: string
      percentage: number
    }
    cpu: {
      loadAverage: number[]
      usage?: number
    }
    disk: {
      used: string
      total: string
      percentage: number
    }
  }
  services: {
    [key: string]: 'up' | 'down' | 'unknown'
  }
}

// 启动时间
const startTime = Date.now()

/**
 * 基础健康检查
 */
export async function getHealthStatus(req: Request, res: Response) {
  try {
    const healthStatus: HealthStatus = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: Math.floor((Date.now() - startTime) / 1000),
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      database: await checkDatabaseHealth(),
      system: await getSystemInfo(),
      services: await checkServices()
    }

    // 根据各项检查结果确定整体状态
    healthStatus.status = determineOverallStatus(healthStatus)

    // 记录告警
    logHealthAlerts(healthStatus)

    // 根据状态返回相应的HTTP状态码
    const httpStatus = healthStatus.status === 'healthy' ? 200 :
                      healthStatus.status === 'degraded' ? 200 : 503

    res.status(httpStatus).json(healthStatus)

  } catch (error: any) {
    console.error('健康检查失败:', error)
    
    res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error.message || '健康检查服务异常',
      uptime: Math.floor((Date.now() - startTime) / 1000)
    })
  }
}

/**
 * 简化的健康检查（用于负载均衡器等）
 */
export function getSimpleHealth(req: Request, res: Response) {
  res.json({ 
    status: 'OK', 
    timestamp: new Date().toISOString(),
    uptime: Math.floor((Date.now() - startTime) / 1000)
  })
}

/**
 * 检查数据库健康状态
 */
async function checkDatabaseHealth(): Promise<HealthStatus['database']> {
  try {
    const db = getDatabase()
    if (!db) {
      return {
        status: 'disconnected',
        error: '数据库连接未初始化'
      }
    }

    const startTime = Date.now()
    
    // 执行简单查询测试连接
    await new Promise<void>((resolve, reject) => {
      db.get('SELECT 1 as test', (err, row) => {
        if (err) {
          reject(err)
        } else {
          resolve()
        }
      })
    })

    const responseTime = Date.now() - startTime

    return {
      status: 'connected',
      responseTime
    }

  } catch (error: any) {
    return {
      status: 'error',
      error: error.message || '数据库连接测试失败'
    }
  }
}

/**
 * 获取系统信息
 */
async function getSystemInfo(): Promise<HealthStatus['system']> {
  try {
    // 内存信息
    const totalMemory = os.totalmem()
    const freeMemory = os.freemem()
    const usedMemory = totalMemory - freeMemory
    const memoryPercentage = Math.round((usedMemory / totalMemory) * 100)

    // CPU信息
    const loadAverage = os.loadavg()

    // 磁盘信息（简化版，只检查当前目录）
    const diskInfo = await getDiskInfo()

    return {
      memory: {
        used: formatBytes(usedMemory),
        total: formatBytes(totalMemory),
        percentage: memoryPercentage
      },
      cpu: {
        loadAverage,
        usage: Math.round(loadAverage[0] * 100) // 简化的CPU使用率
      },
      disk: diskInfo
    }

  } catch (error) {
    console.error('获取系统信息失败:', error)
    return {
      memory: { used: 'N/A', total: 'N/A', percentage: 0 },
      cpu: { loadAverage: [0, 0, 0], usage: 0 },
      disk: { used: 'N/A', total: 'N/A', percentage: 0 }
    }
  }
}

/**
 * 获取磁盘信息
 */
async function getDiskInfo(): Promise<{ used: string; total: string; percentage: number }> {
  try {
    const stats = await fs.promises.statfs ? 
      fs.promises.statfs(process.cwd()) : 
      null

    if (stats) {
      const total = stats.blocks * stats.bsize
      const free = stats.bavail * stats.bsize
      const used = total - free
      const percentage = Math.round((used / total) * 100)

      return {
        used: formatBytes(used),
        total: formatBytes(total),
        percentage
      }
    }
  } catch (error) {
    // 如果无法获取磁盘信息，返回默认值
  }

  return {
    used: 'N/A',
    total: 'N/A',
    percentage: 0
  }
}

/**
 * 检查相关服务状态
 */
async function checkServices(): Promise<HealthStatus['services']> {
  const services: HealthStatus['services'] = {}

  // 检查日志目录是否可写
  try {
    const logsDir = path.join(process.cwd(), 'logs')
    await fs.promises.access(logsDir, fs.constants.W_OK)
    services.logs = 'up'
  } catch {
    services.logs = 'down'
  }

  // 检查数据目录是否可写
  try {
    const dataDir = path.join(process.cwd(), 'data')
    await fs.promises.access(dataDir, fs.constants.W_OK)
    services.storage = 'up'
  } catch {
    services.storage = 'down'
  }

  return services
}

/**
 * 确定整体健康状态
 */
function determineOverallStatus(health: HealthStatus): 'healthy' | 'degraded' | 'unhealthy' {
  // 数据库连接是关键服务
  if (health.database.status === 'error' || health.database.status === 'disconnected') {
    return 'unhealthy'
  }

  // 内存使用率过高
  if (health.system.memory.percentage > 90) {
    return 'degraded'
  }

  // CPU负载过高
  if (health.system.cpu.loadAverage[0] > 2) {
    return 'degraded'
  }

  // 磁盘使用率过高
  if (health.system.disk.percentage > 95) {
    return 'degraded'
  }

  // 关键服务异常
  const criticalServices = ['logs', 'storage']
  const downServices = criticalServices.filter(service => 
    health.services[service] === 'down'
  )
  
  if (downServices.length > 0) {
    return 'degraded'
  }

  return 'healthy'
}

/**
 * 格式化字节数
 */
function formatBytes(bytes: number): string {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * 格式化运行时间
 */
export function formatUptime(seconds: number): string {
  const days = Math.floor(seconds / 86400)
  const hours = Math.floor((seconds % 86400) / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60

  const parts = []
  if (days > 0) parts.push(`${days}天`)
  if (hours > 0) parts.push(`${hours}小时`)
  if (minutes > 0) parts.push(`${minutes}分钟`)
  if (secs > 0 || parts.length === 0) parts.push(`${secs}秒`)

  return parts.join(' ')
}

/**
 * 记录健康检查告警
 */
function logHealthAlerts(health: HealthStatus) {
  // 数据库连接异常
  if (health.database.status === 'error' || health.database.status === 'disconnected') {
    alertError(
      'DATABASE',
      `数据库连接异常: ${health.database.error || '连接失败'}`,
      { status: health.database.status, responseTime: health.database.responseTime },
      'HealthCheck'
    )
  }

  // 内存使用率过高
  if (health.system.memory.percentage > 85) {
    const level = health.system.memory.percentage > 95 ? alertError : alertWarning
    level(
      'MEMORY',
      `内存使用率过高: ${health.system.memory.percentage}% (${health.system.memory.used}/${health.system.memory.total})`,
      { percentage: health.system.memory.percentage },
      'HealthCheck'
    )
  }

  // CPU负载过高
  if (health.system.cpu.loadAverage[0] > 1.5) {
    const level = health.system.cpu.loadAverage[0] > 3 ? alertError : alertWarning
    level(
      'CPU',
      `CPU负载过高: ${health.system.cpu.loadAverage[0].toFixed(2)}`,
      { loadAverage: health.system.cpu.loadAverage },
      'HealthCheck'
    )
  }

  // 磁盘使用率过高
  if (health.system.disk.percentage > 90) {
    const level = health.system.disk.percentage > 98 ? alertError : alertWarning
    level(
      'DISK',
      `磁盘使用率过高: ${health.system.disk.percentage}% (${health.system.disk.used}/${health.system.disk.total})`,
      { percentage: health.system.disk.percentage },
      'HealthCheck'
    )
  }

  // 关键服务异常
  Object.entries(health.services).forEach(([service, status]) => {
    if (status === 'down') {
      alertWarning(
        'SERVICE',
        `关键服务异常: ${service}`,
        { service, status },
        'HealthCheck'
      )
    }
  })

  // 整体状态异常
  if (health.status === 'unhealthy') {
    alertError(
      'SYSTEM',
      '系统整体状态异常',
      { status: health.status, uptime: health.uptime },
      'HealthCheck'
    )
  } else if (health.status === 'degraded') {
    alertWarning(
      'SYSTEM',
      '系统性能下降',
      { status: health.status, uptime: health.uptime },
      'HealthCheck'
    )
  }
}
