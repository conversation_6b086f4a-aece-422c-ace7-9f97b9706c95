<template>
  <ErrorBoundary
    :on-error="handleComponentError"
    :on-retry="handleRetry"
    fallback-message="成品管理模块遇到问题，请重试"
  >
    <div class="products-container">
      <!-- 页面标题 -->
      <div class="page-title">
        <h1>成品管理</h1>
      </div>

    <!-- 操作栏 - 左右分布 -->
    <div class="operations-bar">
      <div class="operations-left">
        <el-input
          v-model="searchQuery"
          placeholder="搜索成品编码、名称或规格"
          :style="{ width: isMobile ? '100%' : '300px', maxWidth: '400px' }"
          clearable
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
      <div class="operations-right">
        <el-button type="primary" @click="showAddDialog = true">
          <el-icon><Plus /></el-icon>
          新增成品
        </el-button>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <el-table
        v-loading="loading"
        :data="products"
        style="width: 100%"
        stripe
        :class="{ 'mobile-table': isMobile }"
      >
        <el-table-column
          prop="code"
          label="编码"
          :min-width="isMobile ? 100 : 120"
          :width="isMobile ? 100 : undefined"
        />
        <el-table-column
          prop="name"
          label="名称"
          :min-width="isMobile ? 120 : 150"
          :width="isMobile ? 120 : undefined"
        />
        <el-table-column
          v-if="!isMobile"
          prop="specification"
          label="规格"
          min-width="120"
        />
        <el-table-column
          prop="unit"
          label="单位"
          :min-width="isMobile ? 60 : 80"
          :width="isMobile ? 60 : undefined"
        />
        <el-table-column
          prop="cost_price"
          label="成本价"
          :min-width="isMobile ? 90 : 100"
          :width="isMobile ? 90 : undefined"
        >
          <template #default="{ row }">
            ¥{{ row.cost_price.toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="sale_price"
          label="销售价"
          :min-width="isMobile ? 90 : 100"
          :width="isMobile ? 90 : undefined"
        >
          <template #default="{ row }">
            ¥{{ row.sale_price.toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column
          v-if="!isMobile"
          prop="current_stock"
          label="当前库存"
          min-width="100"
        />
        <el-table-column
          v-if="!isMobile"
          prop="stock_min"
          label="最小库存"
          min-width="100"
        />
        <el-table-column
          v-if="!isMobile"
          prop="stock_max"
          label="最大库存"
          min-width="100"
        />
        <el-table-column
          v-if="!isMobile"
          prop="created_at"
          label="创建时间"
          min-width="160"
        >
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          :width="isMobile ? 120 : 150"
          fixed="right"
        >
          <template #default="{ row }">
            <div class="table-actions">
              <el-button
                :size="isMobile ? 'small' : 'default'"
                type="primary"
                link
                @click="editProduct(row)"
              >
                编辑
              </el-button>
              <el-button
                :size="isMobile ? 'small' : 'default'"
                type="danger"
                link
                @click="deleteProduct(row)"
              >
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 新增/编辑弹窗 -->
    <el-dialog
      v-model="showAddDialog"
      :title="editingProduct ? '编辑成品' : '新增成品'"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="成品编码" prop="code">
              <el-input v-model="formData.code" placeholder="请输入成品编码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="成品名称" prop="name">
              <el-input v-model="formData.name" placeholder="请输入成品名称" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="规格型号" prop="specification">
              <el-input v-model="formData.specification" placeholder="请输入规格型号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="计量单位" prop="unit">
              <el-input v-model="formData.unit" placeholder="请输入计量单位" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="成本价" prop="cost_price">
              <el-input-number
                v-model="formData.cost_price"
                :min="0"
                :precision="2"
                style="width: 100%"
                placeholder="请输入成本价"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="销售价" prop="sale_price">
              <el-input-number
                v-model="formData.sale_price"
                :min="0"
                :precision="2"
                style="width: 100%"
                placeholder="请输入销售价"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="当前库存" prop="current_stock">
              <el-input-number
                v-model="formData.current_stock"
                :min="0"
                style="width: 100%"
                placeholder="当前库存"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="最小库存" prop="stock_min">
              <el-input-number
                v-model="formData.stock_min"
                :min="0"
                style="width: 100%"
                placeholder="最小库存"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="最大库存" prop="stock_max">
              <el-input-number
                v-model="formData.stock_max"
                :min="0"
                style="width: 100%"
                placeholder="最大库存"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showAddDialog = false">取消</el-button>
          <el-button type="primary" :loading="submitLoading" @click="handleSubmit">
            {{ editingProduct ? '更新' : '创建' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
  </ErrorBoundary>
</template>

<script setup lang="ts">
import { productApi, type Product, type ProductForm } from '@/api/products'
import ErrorBoundary from '@/components/ErrorBoundary.vue'
import { useWindowSize } from '@/composables/useWindowSize'
import { Plus, Search } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { onMounted, reactive, ref } from 'vue'

// 使用全局窗口大小管理器
const { isMobile, isTablet, isDesktop, isLargeScreen } = useWindowSize()

// 响应式数据
const products = ref<Product[]>([])
const loading = ref(false)
const submitLoading = ref(false)
const showAddDialog = ref(false)
const editingProduct = ref<Product | null>(null)
const searchQuery = ref('')
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 表单引用和数据
const formRef = ref<FormInstance>()
const formData = reactive<ProductForm>({
  code: '',
  name: '',
  specification: '',
  unit: '',
  cost_price: 0,
  sale_price: 0,
  stock_min: 0,
  stock_max: 0,
  current_stock: 0,
  status: 'active'
})

// 表单验证规则
const formRules: FormRules = {
  code: [
    { required: true, message: '请输入成品编码', trigger: 'blur' },
    { min: 2, max: 50, message: '编码长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入成品名称', trigger: 'blur' },
    { min: 2, max: 100, message: '名称长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  unit: [
    { required: true, message: '请输入计量单位', trigger: 'blur' },
    { min: 1, max: 20, message: '单位长度在 1 到 20 个字符', trigger: 'blur' }
  ],
  cost_price: [
    { required: true, message: '请输入成本价', trigger: 'blur' },
    { type: 'number', min: 0, message: '成本价不能为负数', trigger: 'blur' }
  ],
  sale_price: [
    { required: true, message: '请输入销售价', trigger: 'blur' },
    { type: 'number', min: 0, message: '销售价不能为负数', trigger: 'blur' }
  ],
  stock_min: [
    { required: true, message: '请输入最小库存', trigger: 'blur' },
    { type: 'number', min: 0, message: '最小库存不能为负数', trigger: 'blur' }
  ],
  stock_max: [
    { required: true, message: '请输入最大库存', trigger: 'blur' },
    { type: 'number', min: 0, message: '最大库存不能为负数', trigger: 'blur' }
  ]
}

// 获取成品列表
async function fetchProducts() {
  try {
    loading.value = true
    const response = await productApi.getProducts({
      page: currentPage.value,
      pageSize: pageSize.value,
      search: searchQuery.value
    })

    products.value = response.products
    total.value = response.total
  } catch (error) {
    console.error('获取成品列表失败:', error)
    ElMessage.error('获取成品列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索处理
function handleSearch() {
  currentPage.value = 1
  fetchProducts()
}

// 分页大小变化
function handleSizeChange(newSize: number) {
  pageSize.value = newSize
  currentPage.value = 1
  fetchProducts()
}

// 当前页变化
function handleCurrentChange(newPage: number) {
  currentPage.value = newPage
  fetchProducts()
}

// 重置表单
function resetForm() {
  Object.assign(formData, {
    code: '',
    name: '',
    specification: '',
    unit: '',
    cost_price: 0,
    sale_price: 0,
    stock_min: 0,
    stock_max: 0,
    current_stock: 0,
    status: 'active'
  })
  editingProduct.value = null
  formRef.value?.clearValidate()
}

// 编辑成品
function editProduct(product: Product) {
  editingProduct.value = product
  Object.assign(formData, {
    code: product.code,
    name: product.name,
    specification: product.specification || '',
    unit: product.unit,
    cost_price: product.cost_price,
    sale_price: product.sale_price,
    stock_min: product.stock_min,
    stock_max: product.stock_max,
    current_stock: product.current_stock,
    status: product.status
  })
  showAddDialog.value = true
}

// 删除成品
async function deleteProduct(product: Product) {
  try {
    await ElMessageBox.confirm(
      `确定要删除成品 "${product.name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await productApi.deleteProduct(product.id)
    ElMessage.success('删除成功')
    fetchProducts()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('删除成品失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 提交表单
async function handleSubmit() {
  try {
    await formRef.value?.validate()

    // 验证库存逻辑
    if (formData.stock_min > formData.stock_max) {
      ElMessage.error('最小库存不能大于最大库存')
      return
    }

    submitLoading.value = true

    if (editingProduct.value) {
      // 更新成品
      await productApi.updateProduct(editingProduct.value.id, formData)
      ElMessage.success('更新成功')
    } else {
      // 创建成品
      await productApi.createProduct(formData)
      ElMessage.success('创建成功')
    }

    showAddDialog.value = false
    resetForm()
    fetchProducts()
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败')
  } finally {
    submitLoading.value = false
  }
}

// 格式化日期
function formatDate(dateString: string) {
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 监听弹窗关闭
function handleDialogClose() {
  resetForm()
}

// 错误处理函数
function handleComponentError(error: Error, instance: any) {
  console.error('ProductsView 组件错误:', error)
  ElMessage.error('成品管理模块发生错误，请重试')
}

function handleRetry() {
  console.log('重试成品管理模块')
  // 重新获取数据
  fetchProducts()
}

// 组件挂载时获取数据
onMounted(() => {
  fetchProducts()
})
</script>

<style scoped>
.products-container {
  padding: 20px;
}

.page-title {
  margin-bottom: 20px;
}

.page-title h1 {
  margin: 0;
  color: #333;
  font-size: 24px;
}

.operations-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  gap: 16px;
}

.operations-left {
  flex: 1;
  display: flex;
  align-items: center;
}

.operations-right {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.table-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.table-container {
  overflow-x: auto;
  width: 100%;
}

.mobile-table {
  min-width: 600px;
}

/* 确保表格充分利用空间 */
.el-table {
  width: 100% !important;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

/* 响应式样式 */
@media (max-width: 767px) {
  .operations-bar {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .operations-left {
    width: 100%;
  }

  .operations-right {
    width: 100%;
    justify-content: center;
  }

  .page-title h1 {
    font-size: 20px;
  }

  .pagination {
    text-align: center;
  }

  .products-container {
    padding: 16px;
  }
}

@media (min-width: 768px) and (max-width: 1023px) {
  .products-container {
    padding: 18px;
  }

  .page-title h1 {
    font-size: 22px;
  }

  .operations-bar {
    gap: 20px;
  }
}

@media (min-width: 1024px) {
  .products-container {
    padding: 20px;
  }

  .operations-bar {
    margin-bottom: 24px;
    gap: 24px;
  }

  .page-title {
    margin-bottom: 24px;
  }
}

@media (min-width: 1400px) {
  .products-container {
    padding: 24px;
  }
}
</style>
