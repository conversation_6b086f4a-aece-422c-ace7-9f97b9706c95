/**
 * 错误信息收集和上报服务
 * 为30人工厂提供简单实用的错误追踪功能
 */

import { ElMessage } from 'element-plus'

export interface ErrorReport {
  // 基本错误信息
  message: string
  stack?: string
  
  // 上下文信息
  component?: string
  action?: string
  url: string
  timestamp: string
  
  // 用户信息
  userId?: string
  userAgent: string
  
  // 系统信息
  viewport: {
    width: number
    height: number
  }
  
  // 错误级别
  level: 'error' | 'warning' | 'info'
  
  // 额外数据
  extra?: Record<string, any>
}

export interface ErrorReportingConfig {
  // 是否启用错误上报
  enabled: boolean
  
  // 本地存储的最大错误数量
  maxLocalErrors: number
  
  // 是否在开发环境显示详细错误
  showDetailInDev: boolean
  
  // 错误上报的API端点
  reportEndpoint?: string
  
  // 是否自动上报
  autoReport: boolean
  
  // 上报间隔（毫秒）
  reportInterval: number
}

class ErrorReportingService {
  private config: ErrorReportingConfig
  private localErrors: ErrorReport[] = []
  private reportTimer: number | null = null

  constructor(config: Partial<ErrorReportingConfig> = {}) {
    this.config = {
      enabled: true,
      maxLocalErrors: 50,
      showDetailInDev: import.meta.env.DEV,
      autoReport: false, // 30人工厂暂时不自动上报，避免网络开销
      reportInterval: 60000, // 1分钟
      ...config
    }

    this.initErrorHandlers()
    
    if (this.config.autoReport && this.config.reportEndpoint) {
      this.startAutoReport()
    }
  }

  /**
   * 初始化全局错误处理器
   */
  private initErrorHandlers() {
    // 捕获未处理的Promise拒绝
    window.addEventListener('unhandledrejection', (event) => {
      this.reportError({
        message: `未处理的Promise拒绝: ${event.reason}`,
        stack: event.reason?.stack,
        component: 'Global',
        action: 'unhandledrejection',
        level: 'error'
      })
    })

    // 捕获全局JavaScript错误
    window.addEventListener('error', (event) => {
      this.reportError({
        message: event.message,
        stack: event.error?.stack,
        component: 'Global',
        action: 'javascript_error',
        level: 'error',
        extra: {
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno
        }
      })
    })
  }

  /**
   * 报告错误
   */
  reportError(errorInfo: Partial<ErrorReport>) {
    if (!this.config.enabled) return

    const report: ErrorReport = {
      message: errorInfo.message || '未知错误',
      stack: errorInfo.stack,
      component: errorInfo.component,
      action: errorInfo.action,
      url: window.location.href,
      timestamp: new Date().toISOString(),
      userId: this.getUserId(),
      userAgent: navigator.userAgent,
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight
      },
      level: errorInfo.level || 'error',
      extra: errorInfo.extra
    }

    // 存储到本地
    this.storeLocalError(report)

    // 在开发环境显示详细信息
    if (this.config.showDetailInDev) {
      console.group(`🚨 错误报告 [${report.level.toUpperCase()}]`)
      console.error('消息:', report.message)
      console.error('组件:', report.component)
      console.error('操作:', report.action)
      console.error('堆栈:', report.stack)
      console.error('完整报告:', report)
      console.groupEnd()
    }

    // 显示用户友好的提示
    this.showUserNotification(report)

    // 立即上报（如果配置了端点）
    if (this.config.reportEndpoint) {
      this.sendReport(report)
    }
  }

  /**
   * 存储错误到本地
   */
  private storeLocalError(report: ErrorReport) {
    this.localErrors.push(report)

    // 限制本地存储的错误数量
    if (this.localErrors.length > this.config.maxLocalErrors) {
      this.localErrors = this.localErrors.slice(-this.config.maxLocalErrors)
    }

    // 同时存储到localStorage（便于调试）
    try {
      const stored = JSON.parse(localStorage.getItem('app_errors') || '[]')
      stored.push(report)
      
      // 只保留最近的错误
      const recent = stored.slice(-20)
      localStorage.setItem('app_errors', JSON.stringify(recent))
    } catch (error) {
      console.warn('无法存储错误到localStorage:', error)
    }
  }

  /**
   * 显示用户通知
   */
  private showUserNotification(report: ErrorReport) {
    if (report.level === 'error') {
      ElMessage.error({
        message: '系统遇到错误，已记录问题详情',
        duration: 3000,
        showClose: true
      })
    } else if (report.level === 'warning') {
      ElMessage.warning({
        message: '系统警告，请注意',
        duration: 2000
      })
    }
  }

  /**
   * 发送错误报告到服务器
   */
  private async sendReport(report: ErrorReport) {
    if (!this.config.reportEndpoint) return

    try {
      await fetch(this.config.reportEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(report)
      })
    } catch (error) {
      console.warn('错误上报失败:', error)
      // 上报失败不影响用户使用，静默处理
    }
  }

  /**
   * 获取用户ID
   */
  private getUserId(): string {
    return localStorage.getItem('userId') || 
           sessionStorage.getItem('userId') || 
           'anonymous'
  }

  /**
   * 开始自动上报
   */
  private startAutoReport() {
    if (this.reportTimer) return

    this.reportTimer = window.setInterval(() => {
      this.batchReport()
    }, this.config.reportInterval)
  }

  /**
   * 停止自动上报
   */
  stopAutoReport() {
    if (this.reportTimer) {
      clearInterval(this.reportTimer)
      this.reportTimer = null
    }
  }

  /**
   * 批量上报错误
   */
  private async batchReport() {
    if (this.localErrors.length === 0 || !this.config.reportEndpoint) return

    try {
      await fetch(this.config.reportEndpoint + '/batch', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          errors: this.localErrors,
          timestamp: new Date().toISOString()
        })
      })

      // 上报成功后清空本地错误
      this.localErrors = []
    } catch (error) {
      console.warn('批量错误上报失败:', error)
    }
  }

  /**
   * 获取本地存储的错误
   */
  getLocalErrors(): ErrorReport[] {
    return [...this.localErrors]
  }

  /**
   * 清空本地错误
   */
  clearLocalErrors() {
    this.localErrors = []
    localStorage.removeItem('app_errors')
  }

  /**
   * 手动上报所有本地错误
   */
  async reportAllErrors() {
    if (this.localErrors.length === 0) {
      ElMessage.info('没有待上报的错误')
      return
    }

    try {
      await this.batchReport()
      ElMessage.success(`已上报 ${this.localErrors.length} 个错误`)
    } catch (error) {
      ElMessage.error('错误上报失败')
    }
  }

  /**
   * 获取错误统计
   */
  getErrorStats() {
    const errors = this.getLocalErrors()
    const stats = {
      total: errors.length,
      byLevel: {
        error: 0,
        warning: 0,
        info: 0
      },
      byComponent: {} as Record<string, number>,
      recent: errors.slice(-5)
    }

    errors.forEach(error => {
      stats.byLevel[error.level]++
      
      if (error.component) {
        stats.byComponent[error.component] = (stats.byComponent[error.component] || 0) + 1
      }
    })

    return stats
  }
}

// 创建全局错误报告服务实例
export const errorReporting = new ErrorReportingService({
  enabled: true,
  maxLocalErrors: 30, // 30人工厂，适中的存储量
  autoReport: false,  // 不自动上报，减少网络开销
  showDetailInDev: true
})

// 便捷的错误报告函数
export const reportError = (error: Error | string, context?: {
  component?: string
  action?: string
  extra?: Record<string, any>
}) => {
  const errorInfo = typeof error === 'string' 
    ? { message: error }
    : { message: error.message, stack: error.stack }

  errorReporting.reportError({
    ...errorInfo,
    ...context,
    level: 'error'
  })
}

export const reportWarning = (message: string, context?: {
  component?: string
  action?: string
  extra?: Record<string, any>
}) => {
  errorReporting.reportError({
    message,
    ...context,
    level: 'warning'
  })
}

export const reportInfo = (message: string, context?: {
  component?: string
  action?: string
  extra?: Record<string, any>
}) => {
  errorReporting.reportError({
    message,
    ...context,
    level: 'info'
  })
}

// 导出类型
export type { ErrorReport, ErrorReportingConfig }
export { ErrorReportingService }
